import { APIResource } from "../../../resource.js";
import * as Core from "../../../core.js";
import { type Response } from "../../../_shims/index.js";
export declare class Content extends APIResource {
    /**
     * Retrieve Container File Content
     */
    retrieve(containerId: string, fileId: string, options?: Core.RequestOptions): Core.APIPromise<Response>;
}
//# sourceMappingURL=content.d.ts.map