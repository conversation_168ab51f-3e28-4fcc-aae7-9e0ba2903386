"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_node_modules_crewai-ts_crew_HierarchicalManager_js";
exports.ids = ["_rsc_node_modules_crewai-ts_crew_HierarchicalManager_js"];
exports.modules = {

/***/ "(rsc)/./node_modules/crewai-ts/crew/HierarchicalManager.js":
/*!************************************************************!*\
  !*** ./node_modules/crewai-ts/crew/HierarchicalManager.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HierarchicalManager: () => (/* binding */ HierarchicalManager)\n/* harmony export */ });\n/* harmony import */ var _agent_Agent_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../agent/Agent.js */ \"(rsc)/./node_modules/crewai-ts/agent/Agent.js\");\n/**\n * HierarchicalManager\n *\n * Provides optimized implementation for the hierarchical process execution in CrewAI.\n * This manager handles task delegation, parallel execution, and synthesis to maximize\n * performance and resource utilization.\n */\n\n/**\n * HierarchicalManager handles the intelligent orchestration of tasks\n * using a manager agent for optimal execution planning and resource allocation.\n */\nclass HierarchicalManager {\n    /**\n     * Create a manager agent for hierarchical process execution\n     */\n    static async createManagerAgent(config) {\n        // If manager agent is already provided, use it\n        if (config.managerAgent) {\n            return config.managerAgent;\n        }\n        // Use the provided manager LLM or default to a capable model\n        const llm = typeof config.managerLlm === 'string'\n            ? { modelName: config.managerLlm } // Convert string to object with modelName\n            : config.managerLlm;\n        // Create the manager agent with optimized settings\n        const managerConfig = {\n            role: 'Manager',\n            goal: 'Coordinate task execution for optimal results',\n            backstory: 'An expert coordinator who excels at organizing complex workflows',\n            llm,\n            verbose: config.verbose,\n            memory: config.memory,\n            allowDelegation: false // Manager itself shouldn't delegate\n        };\n        return new _agent_Agent_js__WEBPACK_IMPORTED_MODULE_0__.Agent(managerConfig);\n    }\n    /**\n     * Create an execution plan using the manager agent.\n     * Optimized for minimal token usage and efficient planning.\n     */\n    static async createExecutionPlan(manager, tasks, context) {\n        // Create a planning task for the manager\n        const planningTask = {\n            description: 'Create an optimal execution plan for the tasks',\n            role: 'execution_planner',\n            goal: 'Determine the most efficient task execution order'\n        };\n        // Prepare task information for the manager\n        const taskInfo = tasks.map(task => ({\n            id: task.id,\n            description: task.description,\n            agent: { role: task.agent.role },\n            priority: task.priority || 'medium',\n            isAsync: task.isAsync || false\n        }));\n        // Execution plan prompt with built-in performance optimization guidance\n        const planPrompt = `\n      You are organizing the execution of a workflow with the following tasks:\n\n      ${JSON.stringify(taskInfo, null, 2)}\n\n      Based on task dependencies, priorities, and potential for parallelization, create an execution plan.\n      Your plan should include:\n      1. The optimal order of task execution (provide task IDs in sequence)\n      2. Any tasks that can be executed in parallel (group them by numbers)\n      3. Which task results should be included in the context for subsequent tasks\n      4. Whether a final synthesis of all results is required\n\n      For parallelization, group task IDs that can run concurrently and assign each group a number.\n      \n      Your response should be in JSON format:\n      {\n        \"taskOrder\": [\"task-id-1\", 1, \"task-id-3\", ...],\n        \"parallelGroups\": { \"1\": [\"task-id-2\", \"task-id-4\"], ... },\n        \"significantTasks\": [\"task-id-1\", \"task-id-3\", ...],\n        \"synthesisRequired\": true/false\n      }\n      \n      Where:\n      - \"taskOrder\" is the sequence of execution, with numbers representing parallel groups\n      - \"parallelGroups\" maps group numbers to arrays of task IDs that can run in parallel\n      - \"significantTasks\" lists tasks whose results should be included in context\n      - \"synthesisRequired\" indicates if a final synthesis of all results is needed\n\n      Current context: ${context}\n    `;\n        try {\n            // Execute the planning task\n            const executionContext = {\n                task: planningTask,\n                context: planPrompt,\n                tools: []\n            };\n            // Use the executeTask method defined in BaseAgent interface\n            const planningResult = await manager.executeTask({ id: 'planning-task', description: planningTask.description, agent: manager }, planPrompt, []);\n            if (!planningResult || !planningResult.output) {\n                throw new Error('Planning failed: No output from manager agent');\n            }\n            // Parse and validate the execution plan\n            let plan;\n            try {\n                // Extract JSON from the response (in case there's additional text)\n                const jsonMatch = planningResult.output.match(/\\{[\\s\\S]*\\}/);\n                if (!jsonMatch) {\n                    throw new Error('No valid JSON found in planning output');\n                }\n                plan = JSON.parse(jsonMatch[0]);\n            }\n            catch (parseError) {\n                console.error('Error parsing plan JSON:', parseError);\n                throw new Error(`Invalid execution plan format: ${parseError.message}`);\n            }\n            // Validate the plan structure\n            if (!plan.taskOrder || !Array.isArray(plan.taskOrder)) {\n                throw new Error('Invalid execution plan: missing or invalid taskOrder');\n            }\n            if (!plan.parallelGroups || typeof plan.parallelGroups !== 'object') {\n                plan.parallelGroups = {};\n            }\n            return plan;\n        }\n        catch (error) {\n            console.error('Error parsing execution plan:', error);\n            // Fallback to a simple sequential plan if parsing fails\n            return {\n                taskOrder: tasks.map(t => t.id),\n                parallelGroups: {},\n                synthesisRequired: false\n            };\n        }\n    }\n    /**\n     * Efficiently manages execution of tasks according to the execution plan.\n     * Optimized for parallel execution and memory usage.\n     */\n    static async executeTasksWithPlan(plan, tasks, context, options) {\n        // Track completed tasks and their results\n        const completedTaskIds = new Set();\n        let overallContext = context || '';\n        let finalOutput = '';\n        // Map of task IDs to tasks for efficient lookup\n        const taskMap = new Map(tasks.map(task => [task.id, task]));\n        // First, execute tasks in the specified order (including parallel groups)\n        for (const item of plan.taskOrder) {\n            // If this is a parallel group ID, process it\n            if (typeof item === 'number' && plan.parallelGroups[item]) {\n                const group = plan.parallelGroups[item];\n                if (options.verbose) {\n                    console.log(`Executing parallel task group ${item} with ${group.length} tasks`);\n                }\n                // Run tasks in this group in parallel\n                const parallelResults = await Promise.all(group.map(async (taskId) => {\n                    const task = taskMap.get(taskId);\n                    if (!task) {\n                        throw new Error(`Task ${taskId} not found in execution plan`);\n                    }\n                    // Execute the task with the current context\n                    return options.executeTask(task, overallContext);\n                }));\n                // Process results from parallel execution\n                for (let i = 0; i < group.length; i++) {\n                    const taskId = group[i];\n                    const output = parallelResults[i];\n                    // Mark task as completed with type safety\n                    if (taskId && typeof taskId === 'string') {\n                        completedTaskIds.add(taskId);\n                        // Add to context if it's significant (determined by manager)\n                        const significant = (taskId && plan.significantTasks?.includes(taskId)) ?? true;\n                        if (significant && output && output.result) {\n                            overallContext += `\\n\\nTask result: ${output.result}`;\n                            // Update potential final output\n                            finalOutput = output.result;\n                        }\n                    }\n                }\n            }\n            else if (typeof item === 'string') {\n                // Regular sequential task execution\n                const taskId = item;\n                const task = taskMap.get(taskId);\n                if (!task) {\n                    throw new Error(`Task ${taskId} not found in execution plan`);\n                }\n                if (options.verbose) {\n                    console.log(`Executing sequential task: ${task.description}`);\n                }\n                // Execute the task with the current context\n                const output = await options.executeTask(task, overallContext);\n                // Mark task as completed\n                completedTaskIds.add(taskId);\n                // Add to context if it's significant\n                const significant = plan.significantTasks?.includes(taskId) ?? true;\n                if (significant && output && output.result) {\n                    overallContext += `\\n\\nTask result: ${output.result}`;\n                    // Update potential final output (last significant task result)\n                    finalOutput = output.result;\n                }\n            }\n        }\n        return {\n            finalOutput,\n            completedTaskIds,\n            context: overallContext\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/crewai-ts/crew/HierarchicalManager.js\n");

/***/ })

};
;