/**
 * Hybrid Orchestration Integration Layer
 * 
 * This module integrates the revolutionary CrewAI + AutoGen hybrid system
 * with RouKey's existing chat completions API.
 * 
 * Features:
 * - Seamless integration with existing API structure
 * - Multi-role detection and hybrid orchestration triggering
 * - Streaming support for real-time orchestration updates
 * - Dynamic expert consultation during execution
 */

import { HybridOrchestrator, type HybridExecution } from './HybridOrchestrator';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';

export interface HybridOrchestrationResult {
  isHybridOrchestration: boolean;
  executionId?: string;
  streamingResponse?: Response;
  error?: string;
}

export interface HybridAnalysisResult {
  shouldUseHybrid: boolean;
  confidence: number;
  reasoning: string;
  detectedRoles: string[];
  orchestrationType: 'conversational' | 'non_conversational' | 'hybrid';
}

export class HybridIntegration {
  private orchestrator: HybridOrchestrator;
  private supabase: any;

  constructor() {
    this.orchestrator = new HybridOrchestrator();
  }

  private async initializeSupabase() {
    if (!this.supabase) {
      this.supabase = await createSupabaseServerClientOnRequest();
    }
  }

  /**
   * Analyzes if a prompt should trigger hybrid orchestration
   * This replaces the old multi-role detection system
   */
  async analyzeForHybridOrchestration(
    prompt: string,
    messages: any[] = [],
    configId: string
  ): Promise<HybridAnalysisResult> {
    console.log(`[Hybrid Integration] Analyzing prompt for hybrid orchestration: "${prompt.substring(0, 100)}..."`);

    await this.initializeSupabase();

    // Get user's available roles to ensure we only suggest roles they have
    const { data: roleAssignments } = await this.supabase
      .from('role_assignments')
      .select('role_name')
      .eq('custom_api_config_id', configId);

    const availableRoles = roleAssignments?.map((ra: any) => ra.role_name) || [];

    if (availableRoles.length === 0) {
      console.log(`[Hybrid Integration] No role assignments found for config ${configId}`);
      return {
        shouldUseHybrid: false,
        confidence: 0,
        reasoning: 'No role assignments available',
        detectedRoles: [],
        orchestrationType: 'non_conversational'
      };
    }

    // Analyze the prompt for multi-role indicators
    const analysis = await this.performHybridAnalysis(prompt, messages, availableRoles);

    console.log(`[Hybrid Integration] Analysis result:`, analysis);
    return analysis;
  }

  /**
   * Performs the actual hybrid analysis
   */
  private async performHybridAnalysis(
    prompt: string,
    messages: any[],
    availableRoles: string[]
  ): Promise<HybridAnalysisResult> {
    const lowerPrompt = prompt.toLowerCase();
    const detectedRoles: string[] = [];
    let orchestrationType: 'conversational' | 'non_conversational' | 'hybrid' = 'non_conversational';
    let confidence = 0;

    // Multi-role indicators
    const multiRoleKeywords = [
      'and then', 'after that', 'next', 'also', 'additionally', 'furthermore',
      'both', 'multiple', 'various', 'different', 'several'
    ];

    const hasMultiRoleIndicators = multiRoleKeywords.some(keyword => lowerPrompt.includes(keyword));

    // Role detection based on available roles
    const roleKeywords = {
      'brainstorming_ideation': ['brainstorm', 'idea', 'creative', 'innovative', 'concept', 'generate ideas'],
      'coding_backend': ['backend', 'server', 'api', 'database', 'algorithm', 'code'],
      'coding_frontend': ['frontend', 'ui', 'interface', 'react', 'javascript', 'css'],
      'research_synthesis': ['research', 'analyze', 'study', 'investigate', 'information'],
      'writing': ['write', 'content', 'article', 'copy', 'blog', 'documentation'],
      'logic_reasoning': ['solve', 'problem', 'logic', 'reasoning', 'analyze', 'think'],
      'general_chat': ['help', 'assist', 'explain', 'tell me', 'what is']
    };

    // Detect roles based on keywords and availability
    for (const [role, keywords] of Object.entries(roleKeywords)) {
      if (availableRoles.includes(role)) {
        const matchCount = keywords.filter(keyword => lowerPrompt.includes(keyword)).length;
        if (matchCount > 0) {
          detectedRoles.push(role);
          confidence += matchCount * 0.2;
        }
      }
    }

    // Conversational indicators
    const conversationalKeywords = ['discuss', 'conversation', 'chat', 'talk about', 'debate'];
    const hasConversationalIndicators = conversationalKeywords.some(keyword => lowerPrompt.includes(keyword));

    // Determine orchestration type
    if (hasConversationalIndicators) {
      orchestrationType = 'conversational';
      confidence += 0.3;
    } else if (detectedRoles.length > 1 && hasMultiRoleIndicators) {
      orchestrationType = 'hybrid';
      confidence += 0.5;
    }

    // Complex task indicators
    const complexityKeywords = [
      'comprehensive', 'detailed', 'complete', 'full', 'thorough', 'extensive',
      'step by step', 'end to end', 'from scratch', 'entire', 'whole'
    ];
    const hasComplexityIndicators = complexityKeywords.some(keyword => lowerPrompt.includes(keyword));

    if (hasComplexityIndicators) {
      confidence += 0.3;
    }

    // Multi-role task patterns
    const multiRolePatterns = [
      /brainstorm.*and.*code/i,
      /research.*and.*write/i,
      /design.*and.*implement/i,
      /analyze.*and.*create/i,
      /plan.*and.*execute/i
    ];

    const hasMultiRolePatterns = multiRolePatterns.some(pattern => pattern.test(prompt));
    if (hasMultiRolePatterns) {
      confidence += 0.4;
      if (detectedRoles.length < 2) {
        // Add complementary roles
        if (lowerPrompt.includes('brainstorm') && !detectedRoles.includes('coding_backend')) {
          detectedRoles.push('coding_backend');
        }
        if (lowerPrompt.includes('research') && !detectedRoles.includes('writing')) {
          detectedRoles.push('writing');
        }
      }
    }

    // Ensure we have at least one role
    if (detectedRoles.length === 0 && availableRoles.includes('general_chat')) {
      detectedRoles.push('general_chat');
    }

    // Determine if hybrid orchestration should be used
    const shouldUseHybrid = (
      detectedRoles.length > 1 || 
      (detectedRoles.length === 1 && confidence > 0.7) ||
      hasConversationalIndicators
    ) && confidence > 0.4;

    // Cap confidence at 1.0
    confidence = Math.min(confidence, 1.0);

    let reasoning = '';
    if (shouldUseHybrid) {
      reasoning = `Detected ${detectedRoles.length} roles (${detectedRoles.join(', ')}) with ${orchestrationType} orchestration. `;
      if (hasMultiRoleIndicators) reasoning += 'Multi-role indicators found. ';
      if (hasConversationalIndicators) reasoning += 'Conversational approach needed. ';
      if (hasComplexityIndicators) reasoning += 'Complex task requiring coordination. ';
    } else {
      reasoning = `Single-role task detected. Confidence too low (${confidence.toFixed(2)}) for hybrid orchestration.`;
    }

    return {
      shouldUseHybrid,
      confidence,
      reasoning,
      detectedRoles,
      orchestrationType
    };
  }

  /**
   * Initiates REAL hybrid orchestration and returns streaming response
   */
  async initiateHybridOrchestration(
    userId: string,
    configId: string,
    prompt: string,
    messages: any[] = [],
    context: any = {}
  ): Promise<HybridOrchestrationResult> {
    console.log(`[Hybrid Integration] Initiating REAL hybrid orchestration for user ${userId}`);

    try {
      // Create a simplified execution object for real orchestration
      const execution = {
        id: `hybrid_exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        userId,
        configId,
        originalPrompt: prompt,
        context: {
          ...context,
          messages,
          timestamp: new Date()
        }
      };

      console.log(`[Hybrid Integration] Real hybrid orchestration started with execution ID: ${execution.id}`);

      // Create streaming response with REAL API calls
      const streamingResponse = await this.createHybridStreamingResponse(execution);

      return {
        isHybridOrchestration: true,
        executionId: execution.id,
        streamingResponse
      };

    } catch (error) {
      console.error(`[Hybrid Integration] Failed to initiate hybrid orchestration:`, error);
      return {
        isHybridOrchestration: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Creates a REAL streaming response for hybrid orchestration
   * Actually executes the roles and returns their real responses
   */
  private async createHybridStreamingResponse(execution: HybridExecution): Promise<Response> {
    console.log(`[Hybrid Integration] Creating REAL streaming response for execution ${execution.id}`);

    const encoder = new TextEncoder();

    const stream = new ReadableStream({
      async start(controller) {
        try {
          // Get the Jina classification results from context
          const jinaClassification = execution.context?.jinaClassification;
          if (!jinaClassification || !jinaClassification.roles) {
            throw new Error('No Jina classification results found');
          }

          const detectedRoles = jinaClassification.roles;
          console.log(`[Hybrid Integration] Executing ${detectedRoles.length} roles: ${detectedRoles.map((r: any) => r.roleId).join(', ')}`);

          // Execute each role sequentially and stream the results
          for (let i = 0; i < detectedRoles.length; i++) {
            const roleData = detectedRoles[i];
            const roleId = roleData.roleId;

            console.log(`[Hybrid Integration] Executing role ${i + 1}/${detectedRoles.length}: ${roleId}`);

            // Send role start notification
            const roleStartChunk = {
              id: crypto.randomUUID(),
              object: "chat.completion.chunk",
              created: Math.floor(Date.now() / 1000),
              model: "rokey-hybrid-orchestration",
              choices: [{
                index: 0,
                delta: {
                  content: `\n## 🤖 ${roleId.replace('_', ' ').toUpperCase()} Agent\n\n`
                },
                finish_reason: null
              }]
            };
            controller.enqueue(encoder.encode(`data: ${JSON.stringify(roleStartChunk)}\n\n`));

            try {
              // Execute the role with REAL API call
              const roleResponse = await this.executeRoleWithRealAPI(roleId, execution);

              // Stream the role response
              const roleResponseChunk = {
                id: crypto.randomUUID(),
                object: "chat.completion.chunk",
                created: Math.floor(Date.now() / 1000),
                model: "rokey-hybrid-orchestration",
                choices: [{
                  index: 0,
                  delta: {
                    content: roleResponse + '\n\n'
                  },
                  finish_reason: null
                }]
              };
              controller.enqueue(encoder.encode(`data: ${JSON.stringify(roleResponseChunk)}\n\n`));

            } catch (roleError) {
              console.error(`[Hybrid Integration] Error executing role ${roleId}:`, roleError);

              const errorChunk = {
                id: crypto.randomUUID(),
                object: "chat.completion.chunk",
                created: Math.floor(Date.now() / 1000),
                model: "rokey-hybrid-orchestration",
                choices: [{
                  index: 0,
                  delta: {
                    content: `❌ Error executing ${roleId}: ${roleError instanceof Error ? roleError.message : 'Unknown error'}\n\n`
                  },
                  finish_reason: null
                }]
              };
              controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorChunk)}\n\n`));
            }
          }

          // Send completion
          const completionChunk = {
            id: crypto.randomUUID(),
            object: "chat.completion.chunk",
            created: Math.floor(Date.now() / 1000),
            model: "rokey-hybrid-orchestration",
            choices: [{
              index: 0,
              delta: {
                content: `\n---\n\n✅ **Multi-role orchestration complete!** All ${detectedRoles.length} specialized agents have contributed their expertise.\n`
              },
              finish_reason: "stop"
            }]
          };

          controller.enqueue(encoder.encode(`data: ${JSON.stringify(completionChunk)}\n\n`));
          controller.enqueue(encoder.encode(`data: [DONE]\n\n`));
          controller.close();

        } catch (error) {
          console.error(`[Hybrid Integration] Streaming error:`, error);

          const errorChunk = {
            id: crypto.randomUUID(),
            object: "chat.completion.chunk",
            created: Math.floor(Date.now() / 1000),
            model: "rokey-hybrid-orchestration",
            choices: [{
              index: 0,
              delta: {
                content: `❌ **Multi-role orchestration error:** ${error instanceof Error ? error.message : 'Unknown error'}\n\n`
              },
              finish_reason: "stop"
            }]
          };

          controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorChunk)}\n\n`));
          controller.enqueue(encoder.encode(`data: [DONE]\n\n`));
          controller.close();
        }
      },

      cancel() {
        console.log(`[Hybrid Integration] Client disconnected from hybrid orchestration stream`);
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'X-Accel-Buffering': 'no',
        'X-RoKey-Hybrid-Orchestration': 'true',
        'X-RoKey-Execution-ID': execution.id
      }
    });
  }

  /**
   * Execute a role with REAL API calls to the assigned models
   */
  private async executeRoleWithRealAPI(roleId: string, execution: HybridExecution): Promise<string> {
    await this.initializeSupabase();

    console.log(`[Hybrid Integration] Executing REAL API call for role: ${roleId}`);

    // Get the API key assigned to this role
    const { data: roleAssignment } = await this.supabase
      .from('role_assignments')
      .select('api_key_id')
      .eq('custom_api_config_id', execution.configId)
      .eq('role_name', roleId)
      .single();

    if (!roleAssignment) {
      throw new Error(`No API key assigned to role: ${roleId}`);
    }

    // Get the API key details
    const { data: apiKey } = await this.supabase
      .from('api_keys')
      .select('*')
      .eq('id', roleAssignment.api_key_id)
      .single();

    if (!apiKey || apiKey.status !== 'active') {
      throw new Error(`API key not found or inactive for role: ${roleId}`);
    }

    // Create role-specific prompt
    const rolePrompt = this.createRoleSpecificPrompt(roleId, execution.originalPrompt);

    // Make REAL API call to the assigned model
    const response = await this.callRealAPI(apiKey, rolePrompt);

    return response;
  }

  /**
   * Create a role-specific prompt for the given role
   */
  private createRoleSpecificPrompt(roleId: string, originalPrompt: string): string {
    const rolePrompts = {
      'brainstorming_ideation': `You are a creative brainstorming expert. Focus on generating innovative ideas and creative concepts. Original request: "${originalPrompt}". Provide creative ideas and brainstorming insights.`,

      'coding_backend': `You are a backend development expert. Focus on server-side logic, APIs, databases, and backend architecture. Original request: "${originalPrompt}". Provide backend development solutions and code.`,

      'coding_frontend': `You are a frontend development expert. Focus on user interfaces, client-side code, and user experience. Original request: "${originalPrompt}". Provide frontend development solutions and code.`,

      'writing': `You are a professional writer and content creator. Focus on clear, engaging, and well-structured written content. Original request: "${originalPrompt}". Provide written content and documentation.`,

      'research_synthesis': `You are a research and analysis expert. Focus on gathering information, analyzing data, and synthesizing findings. Original request: "${originalPrompt}". Provide research insights and analysis.`,

      'logic_reasoning': `You are a logical reasoning expert. Focus on problem-solving, analytical thinking, and structured reasoning. Original request: "${originalPrompt}". Provide logical analysis and solutions.`,

      'general_chat': `You are a helpful AI assistant. Provide comprehensive assistance for the user's request. Original request: "${originalPrompt}". Provide helpful and informative responses.`
    };

    return rolePrompts[roleId as keyof typeof rolePrompts] || `You are an expert in ${roleId.replace('_', ' ')}. Original request: "${originalPrompt}". Provide expert assistance in your domain.`;
  }

  /**
   * Make a REAL API call to the assigned model
   */
  private async callRealAPI(apiKey: any, prompt: string): Promise<string> {
    console.log(`[Hybrid Integration] Making REAL API call to ${apiKey.provider} model: ${apiKey.predefined_model_id}`);

    // Import the provider execution function
    const { executeProviderRequest } = await import('@/app/api/v1/chat/completions/route');

    // Prepare the request body in OpenAI format
    const requestBody = {
      model: apiKey.predefined_model_id,
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      stream: false,
      temperature: 0.7,
      max_tokens: 2000
    };

    try {
      // Call the actual provider
      const result = await executeProviderRequest(
        apiKey.provider,
        apiKey.predefined_model_id,
        requestBody,
        apiKey.encrypted_api_key,
        false // not streaming for hybrid orchestration
      );

      if (result.success && result.responseData) {
        // Extract the content from the response
        const content = result.responseData.choices?.[0]?.message?.content || 'No response content';
        return content;
      } else {
        throw new Error(`API call failed: ${result.error}`);
      }

    } catch (error) {
      console.error(`[Hybrid Integration] API call error:`, error);
      throw new Error(`Failed to call ${apiKey.provider} API: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
