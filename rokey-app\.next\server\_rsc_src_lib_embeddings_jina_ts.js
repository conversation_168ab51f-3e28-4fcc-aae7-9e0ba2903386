"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_embeddings_jina_ts";
exports.ids = ["_rsc_src_lib_embeddings_jina_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/embeddings/jina.ts":
/*!************************************!*\
  !*** ./src/lib/embeddings/jina.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MultiKeyJinaEmbeddings: () => (/* binding */ MultiKeyJinaEmbeddings),\n/* harmony export */   jinaEmbeddings: () => (/* binding */ jinaEmbeddings)\n/* harmony export */ });\n/**\r\n * Multi-Key Jina Embeddings v3 Implementation\r\n * Provides automatic key rotation and high rate limits for RouKey\r\n */ class MultiKeyJinaEmbeddings {\n    constructor(){\n        this.currentKeyIndex = 0;\n        this.keyUsage = new Map();\n        this.baseUrl = 'https://api.jina.ai/v1/embeddings';\n        this.model = 'jina-embeddings-v3';\n        // Load all Jina API keys from environment\n        this.apiKeys = [\n            process.env.JINA_API_KEY,\n            process.env.JINA_API_KEY_2,\n            process.env.JINA_API_KEY_3,\n            process.env.JINA_API_KEY_4,\n            process.env.JINA_API_KEY_5,\n            process.env.JINA_API_KEY_6,\n            process.env.JINA_API_KEY_7,\n            process.env.JINA_API_KEY_9,\n            process.env.JINA_API_KEY_10\n        ].filter(Boolean);\n        if (this.apiKeys.length === 0) {\n            throw new Error('No Jina API keys found in environment variables');\n        }\n        console.log(`[Jina Embeddings] Initialized with ${this.apiKeys.length} API keys`);\n        // Initialize usage stats for each key\n        this.apiKeys.forEach((key)=>{\n            this.keyUsage.set(key, {\n                requests: 0,\n                tokens: 0,\n                lastUsed: new Date(),\n                errors: 0\n            });\n        });\n    }\n    /**\r\n   * Get the next API key using round-robin rotation\r\n   */ getNextKey() {\n        const key = this.apiKeys[this.currentKeyIndex];\n        this.currentKeyIndex = (this.currentKeyIndex + 1) % this.apiKeys.length;\n        return key;\n    }\n    /**\r\n   * Get the best available API key based on usage and error rates\r\n   */ getBestKey() {\n        // For now, use simple round-robin\n        // TODO: Implement smart selection based on usage stats\n        return this.getNextKey();\n    }\n    /**\r\n   * Update usage statistics for a key\r\n   */ updateKeyUsage(apiKey, tokens, isError = false) {\n        const stats = this.keyUsage.get(apiKey);\n        if (stats) {\n            stats.requests++;\n            stats.tokens += tokens;\n            stats.lastUsed = new Date();\n            if (isError) {\n                stats.errors++;\n                stats.lastError = new Date();\n            }\n        }\n    }\n    /**\r\n   * Generate embedding for a single text input\r\n   */ async embedQuery(text) {\n        const maxRetries = this.apiKeys.length;\n        let lastError = null;\n        for(let attempt = 0; attempt < maxRetries; attempt++){\n            try {\n                const apiKey = this.getBestKey();\n                console.log(`[Jina Embeddings] Attempt ${attempt + 1}/${maxRetries} with key ${this.apiKeys.indexOf(apiKey) + 1}`);\n                const response = await fetch(this.baseUrl, {\n                    method: 'POST',\n                    headers: {\n                        'Authorization': `Bearer ${apiKey}`,\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        model: this.model,\n                        input: [\n                            text\n                        ],\n                        normalized: true,\n                        embedding_type: 'float'\n                    })\n                });\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    if (response.status === 429) {\n                        console.log(`[Jina Embeddings] Rate limit hit for key ${this.apiKeys.indexOf(apiKey) + 1}, trying next key...`);\n                        this.updateKeyUsage(apiKey, 0, true);\n                        continue;\n                    }\n                    throw new Error(`HTTP ${response.status}: ${errorText}`);\n                }\n                const data = await response.json();\n                if (!data.data || data.data.length === 0) {\n                    throw new Error('No embedding data returned from Jina API');\n                }\n                const embedding = data.data[0].embedding;\n                // Update usage stats\n                this.updateKeyUsage(apiKey, data.usage?.total_tokens || text.length);\n                console.log(`[Jina Embeddings] Success with key ${this.apiKeys.indexOf(apiKey) + 1} (${embedding.length} dimensions, ${data.usage?.total_tokens || 'unknown'} tokens)`);\n                return embedding;\n            } catch (error) {\n                lastError = error;\n                console.log(`[Jina Embeddings] Attempt ${attempt + 1} failed:`, error.message);\n                // If this is the last attempt, throw the error\n                if (attempt === maxRetries - 1) {\n                    break;\n                }\n            }\n        }\n        // All keys failed\n        console.error(`[Jina Embeddings] All ${maxRetries} API keys failed`);\n        throw new Error(`All Jina API keys failed. Last error: ${lastError?.message || 'Unknown error'}`);\n    }\n    /**\r\n   * Generate embeddings for multiple texts (batch processing)\r\n   */ async embedDocuments(texts) {\n        // For now, process sequentially to avoid overwhelming the API\n        // TODO: Implement smart batching based on rate limits\n        const embeddings = [];\n        for(let i = 0; i < texts.length; i++){\n            console.log(`[Jina Embeddings] Processing document ${i + 1}/${texts.length}`);\n            const embedding = await this.embedQuery(texts[i]);\n            embeddings.push(embedding);\n            // Small delay to respect rate limits\n            if (i < texts.length - 1) {\n                await new Promise((resolve)=>setTimeout(resolve, 100));\n            }\n        }\n        return embeddings;\n    }\n    /**\r\n   * Get usage statistics for all keys\r\n   */ getUsageStats() {\n        const stats = {};\n        this.apiKeys.forEach((key, index)=>{\n            const keyStats = this.keyUsage.get(key);\n            if (keyStats) {\n                stats[`key_${index + 1}`] = {\n                    ...keyStats\n                };\n            }\n        });\n        return stats;\n    }\n    /**\r\n   * Get total capacity across all keys\r\n   */ getTotalCapacity() {\n        return {\n            totalKeys: this.apiKeys.length,\n            estimatedRPM: this.apiKeys.length * 500,\n            estimatedTokensPerMonth: this.apiKeys.length * 1000000\n        };\n    }\n}\n// Export a singleton instance\nconst jinaEmbeddings = new MultiKeyJinaEmbeddings();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/embeddings/jina.ts\n");

/***/ })

};
;