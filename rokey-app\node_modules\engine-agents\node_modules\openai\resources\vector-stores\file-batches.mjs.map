{"version": 3, "file": "file-batches.mjs", "sourceRoot": "", "sources": ["../../src/resources/vector-stores/file-batches.ts"], "names": [], "mappings": "AAAA,sFAAsF;OAE/E,EAAE,WAAW,EAAE;OACf,EAAE,gBAAgB,EAAE;OACpB,EAAE,KAAK,EAAE;OAET,EAAE,mBAAmB,EAAE;OAGvB,EAAE,oBAAoB,EAAE;AAI/B,MAAM,OAAO,WAAY,SAAQ,WAAW;IAC1C;;OAEG;IACH,MAAM,CACJ,aAAqB,EACrB,IAA2B,EAC3B,OAA6B;QAE7B,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,aAAa,eAAe,EAAE;YACvE,IAAI;YACJ,GAAG,OAAO;YACV,OAAO,EAAE,EAAE,aAAa,EAAE,eAAe,EAAE,GAAG,OAAO,EAAE,OAAO,EAAE;SACjE,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,QAAQ,CACN,aAAqB,EACrB,OAAe,EACf,OAA6B;QAE7B,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,aAAa,iBAAiB,OAAO,EAAE,EAAE;YACjF,GAAG,OAAO;YACV,OAAO,EAAE,EAAE,aAAa,EAAE,eAAe,EAAE,GAAG,OAAO,EAAE,OAAO,EAAE;SACjE,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,MAAM,CACJ,aAAqB,EACrB,OAAe,EACf,OAA6B;QAE7B,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,aAAa,iBAAiB,OAAO,SAAS,EAAE;YACzF,GAAG,OAAO;YACV,OAAO,EAAE,EAAE,aAAa,EAAE,eAAe,EAAE,GAAG,OAAO,EAAE,OAAO,EAAE;SACjE,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CACjB,aAAqB,EACrB,IAA2B,EAC3B,OAA2D;QAE3D,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;QACrD,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IAgBD,SAAS,CACP,aAAqB,EACrB,OAAe,EACf,QAAwD,EAAE,EAC1D,OAA6B;QAE7B,IAAI,gBAAgB,CAAC,KAAK,CAAC,EAAE;YAC3B,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;SAC1D;QACD,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAC5B,kBAAkB,aAAa,iBAAiB,OAAO,QAAQ,EAC/D,oBAAoB,EACpB,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,OAAO,EAAE,EAAE,aAAa,EAAE,eAAe,EAAE,GAAG,OAAO,EAAE,OAAO,EAAE,EAAE,CACxF,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,IAAI,CACR,aAAqB,EACrB,OAAe,EACf,OAA2D;QAE3D,MAAM,OAAO,GAA8B,EAAE,GAAG,OAAO,EAAE,OAAO,EAAE,yBAAyB,EAAE,MAAM,EAAE,CAAC;QACtG,IAAI,OAAO,EAAE,cAAc,EAAE;YAC3B,OAAO,CAAC,kCAAkC,CAAC,GAAG,OAAO,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;SACjF;QAED,OAAO,IAAI,EAAE;YACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,OAAO,EAAE;gBAC5E,GAAG,OAAO;gBACV,OAAO;aACR,CAAC,CAAC,YAAY,EAAE,CAAC;YAElB,QAAQ,KAAK,CAAC,MAAM,EAAE;gBACpB,KAAK,aAAa;oBAChB,IAAI,aAAa,GAAG,IAAI,CAAC;oBAEzB,IAAI,OAAO,EAAE,cAAc,EAAE;wBAC3B,aAAa,GAAG,OAAO,CAAC,cAAc,CAAC;qBACxC;yBAAM;wBACL,MAAM,cAAc,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;wBACpE,IAAI,cAAc,EAAE;4BAClB,MAAM,gBAAgB,GAAG,QAAQ,CAAC,cAAc,CAAC,CAAC;4BAClD,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAAE;gCAC5B,aAAa,GAAG,gBAAgB,CAAC;6BAClC;yBACF;qBACF;oBACD,MAAM,KAAK,CAAC,aAAa,CAAC,CAAC;oBAC3B,MAAM;gBACR,KAAK,QAAQ,CAAC;gBACd,KAAK,WAAW,CAAC;gBACjB,KAAK,WAAW;oBACd,OAAO,KAAK,CAAC;aAChB;SACF;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,aAAa,CACjB,aAAqB,EACrB,EAAE,KAAK,EAAE,OAAO,GAAG,EAAE,EAA+C,EACpE,OAAoF;QAEpF,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;YACtC,MAAM,IAAI,KAAK,CACb,gHAAgH,CACjH,CAAC;SACH;QAED,MAAM,qBAAqB,GAAG,OAAO,EAAE,cAAc,IAAI,CAAC,CAAC;QAE3D,kGAAkG;QAClG,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,qBAAqB,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;QAEvE,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAC5B,MAAM,YAAY,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;QACpC,MAAM,UAAU,GAAa,CAAC,GAAG,OAAO,CAAC,CAAC;QAE1C,6FAA6F;QAC7F,qHAAqH;QACrH,KAAK,UAAU,YAAY,CAAC,QAAsC;YAChE,KAAK,IAAI,IAAI,IAAI,QAAQ,EAAE;gBACzB,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE,OAAO,CAAC,CAAC;gBAC1F,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;aAC7B;QACH,CAAC;QAED,mCAAmC;QACnC,MAAM,OAAO,GAAG,KAAK,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAE7E,uCAAuC;QACvC,MAAM,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAEnC,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE;YAC7C,QAAQ,EAAE,UAAU;SACrB,CAAC,CAAC;IACL,CAAC;CACF;AAyHD,OAAO,EAAE,oBAAoB,EAAE,CAAC"}