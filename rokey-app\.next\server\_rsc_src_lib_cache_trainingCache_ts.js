"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_cache_trainingCache_ts";
exports.ids = ["_rsc_src_lib_cache_trainingCache_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/cache/trainingCache.ts":
/*!****************************************!*\
  !*** ./src/lib/cache/trainingCache.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   invalidateTrainingCache: () => (/* binding */ invalidateTrainingCache),\n/* harmony export */   trainingDataCache: () => (/* binding */ trainingDataCache)\n/* harmony export */ });\n// Shared training data cache for cross-endpoint access\n// This allows cache invalidation from upsert operations\nclass TrainingDataCache {\n    set(configId, data, jobId) {\n        this.cache.set(configId, {\n            data,\n            timestamp: Date.now(),\n            jobId\n        });\n        console.log(`[Training Cache] Cached training data for config: ${configId}, jobId: ${jobId}`);\n    }\n    get(configId) {\n        const entry = this.cache.get(configId);\n        if (!entry) return null;\n        // Check if expired\n        if (Date.now() - entry.timestamp > this.TTL) {\n            this.cache.delete(configId);\n            return null;\n        }\n        return entry;\n    }\n    invalidate(configId) {\n        const deleted = this.cache.delete(configId);\n        console.log(`[Training Cache] Invalidated cache for config: ${configId} (${deleted ? 'success' : 'not cached'})`);\n        return deleted;\n    }\n    clear() {\n        this.cache.clear();\n    }\n    cleanup() {\n        const now = Date.now();\n        for (const [key, entry] of this.cache.entries()){\n            if (now - entry.timestamp > this.TTL) {\n                this.cache.delete(key);\n            }\n        }\n    }\n    getStats() {\n        return {\n            size: this.cache.size,\n            keys: Array.from(this.cache.keys())\n        };\n    }\n    constructor(){\n        this.cache = new Map();\n        this.TTL = 300000 // 5 minutes\n        ;\n    }\n}\n// Global instance\nconst trainingDataCache = new TrainingDataCache();\n// Cleanup interval\nsetInterval(()=>{\n    trainingDataCache.cleanup();\n}, 600000); // Every 10 minutes\n// Cache invalidation API endpoint helper\nasync function invalidateTrainingCache(configId) {\n    try {\n        // Call the cache invalidation endpoint\n        const response = await fetch('/api/cache/invalidate', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                type: 'training',\n                configId\n            })\n        });\n        return response.ok;\n    } catch (error) {\n        console.warn('Failed to invalidate training cache:', error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/cache/trainingCache.ts\n");

/***/ })

};
;