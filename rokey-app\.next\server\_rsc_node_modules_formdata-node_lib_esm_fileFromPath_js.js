"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_node_modules_formdata-node_lib_esm_fileFromPath_js";
exports.ids = ["_rsc_node_modules_formdata-node_lib_esm_fileFromPath_js"];
exports.modules = {

/***/ "(rsc)/./node_modules/formdata-node/lib/esm/fileFromPath.js":
/*!************************************************************!*\
  !*** ./node_modules/formdata-node/lib/esm/fileFromPath.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fileFromPath: () => (/* binding */ fileFromPath),\n/* harmony export */   fileFromPathSync: () => (/* binding */ fileFromPathSync),\n/* harmony export */   isFile: () => (/* reexport safe */ _isFile_js__WEBPACK_IMPORTED_MODULE_5__.isFile)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var node_domexception__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! node-domexception */ \"(rsc)/./node_modules/node-domexception/index.js\");\n/* harmony import */ var _File_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./File.js */ \"(rsc)/./node_modules/formdata-node/lib/esm/File.js\");\n/* harmony import */ var _isPlainObject_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./isPlainObject.js */ \"(rsc)/./node_modules/formdata-node/lib/esm/isPlainObject.js\");\n/* harmony import */ var _isFile_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./isFile.js */ \"(rsc)/./node_modules/formdata-node/lib/esm/isFile.js\");\nvar __classPrivateFieldSet = (undefined && undefined.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __classPrivateFieldGet = (undefined && undefined.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _FileFromPath_path, _FileFromPath_start;\n\n\n\n\n\n\nconst MESSAGE = \"The requested file could not be read, \"\n    + \"typically due to permission problems that have occurred after a reference \"\n    + \"to a file was acquired.\";\nclass FileFromPath {\n    constructor(input) {\n        _FileFromPath_path.set(this, void 0);\n        _FileFromPath_start.set(this, void 0);\n        __classPrivateFieldSet(this, _FileFromPath_path, input.path, \"f\");\n        __classPrivateFieldSet(this, _FileFromPath_start, input.start || 0, \"f\");\n        this.name = (0,path__WEBPACK_IMPORTED_MODULE_1__.basename)(__classPrivateFieldGet(this, _FileFromPath_path, \"f\"));\n        this.size = input.size;\n        this.lastModified = input.lastModified;\n    }\n    slice(start, end) {\n        return new FileFromPath({\n            path: __classPrivateFieldGet(this, _FileFromPath_path, \"f\"),\n            lastModified: this.lastModified,\n            size: end - start,\n            start\n        });\n    }\n    async *stream() {\n        const { mtimeMs } = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.stat(__classPrivateFieldGet(this, _FileFromPath_path, \"f\"));\n        if (mtimeMs > this.lastModified) {\n            throw new node_domexception__WEBPACK_IMPORTED_MODULE_2__(MESSAGE, \"NotReadableError\");\n        }\n        if (this.size) {\n            yield* (0,fs__WEBPACK_IMPORTED_MODULE_0__.createReadStream)(__classPrivateFieldGet(this, _FileFromPath_path, \"f\"), {\n                start: __classPrivateFieldGet(this, _FileFromPath_start, \"f\"),\n                end: __classPrivateFieldGet(this, _FileFromPath_start, \"f\") + this.size - 1\n            });\n        }\n    }\n    get [(_FileFromPath_path = new WeakMap(), _FileFromPath_start = new WeakMap(), Symbol.toStringTag)]() {\n        return \"File\";\n    }\n}\nfunction createFileFromPath(path, { mtimeMs, size }, filenameOrOptions, options = {}) {\n    let filename;\n    if ((0,_isPlainObject_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(filenameOrOptions)) {\n        [options, filename] = [filenameOrOptions, undefined];\n    }\n    else {\n        filename = filenameOrOptions;\n    }\n    const file = new FileFromPath({ path, size, lastModified: mtimeMs });\n    if (!filename) {\n        filename = file.name;\n    }\n    return new _File_js__WEBPACK_IMPORTED_MODULE_3__.File([file], filename, {\n        ...options, lastModified: file.lastModified\n    });\n}\nfunction fileFromPathSync(path, filenameOrOptions, options = {}) {\n    const stats = (0,fs__WEBPACK_IMPORTED_MODULE_0__.statSync)(path);\n    return createFileFromPath(path, stats, filenameOrOptions, options);\n}\nasync function fileFromPath(path, filenameOrOptions, options) {\n    const stats = await fs__WEBPACK_IMPORTED_MODULE_0__.promises.stat(path);\n    return createFileFromPath(path, stats, filenameOrOptions, options);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/formdata-node/lib/esm/fileFromPath.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/formdata-node/lib/esm/isPlainObject.js":
/*!*************************************************************!*\
  !*** ./node_modules/formdata-node/lib/esm/isPlainObject.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst getType = (value) => (Object.prototype.toString.call(value).slice(8, -1).toLowerCase());\nfunction isPlainObject(value) {\n    if (getType(value) !== \"object\") {\n        return false;\n    }\n    const pp = Object.getPrototypeOf(value);\n    if (pp === null || pp === undefined) {\n        return true;\n    }\n    const Ctor = pp.constructor && pp.constructor.toString();\n    return Ctor === Object.toString();\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (isPlainObject);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZm9ybWRhdGEtbm9kZS9saWIvZXNtL2lzUGxhaW5PYmplY3QuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlFQUFlLGFBQWEsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxub2RlX21vZHVsZXNcXGZvcm1kYXRhLW5vZGVcXGxpYlxcZXNtXFxpc1BsYWluT2JqZWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGdldFR5cGUgPSAodmFsdWUpID0+IChPYmplY3QucHJvdG90eXBlLnRvU3RyaW5nLmNhbGwodmFsdWUpLnNsaWNlKDgsIC0xKS50b0xvd2VyQ2FzZSgpKTtcbmZ1bmN0aW9uIGlzUGxhaW5PYmplY3QodmFsdWUpIHtcbiAgICBpZiAoZ2V0VHlwZSh2YWx1ZSkgIT09IFwib2JqZWN0XCIpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBjb25zdCBwcCA9IE9iamVjdC5nZXRQcm90b3R5cGVPZih2YWx1ZSk7XG4gICAgaWYgKHBwID09PSBudWxsIHx8IHBwID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIGNvbnN0IEN0b3IgPSBwcC5jb25zdHJ1Y3RvciAmJiBwcC5jb25zdHJ1Y3Rvci50b1N0cmluZygpO1xuICAgIHJldHVybiBDdG9yID09PSBPYmplY3QudG9TdHJpbmcoKTtcbn1cbmV4cG9ydCBkZWZhdWx0IGlzUGxhaW5PYmplY3Q7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/formdata-node/lib/esm/isPlainObject.js\n");

/***/ })

};
;