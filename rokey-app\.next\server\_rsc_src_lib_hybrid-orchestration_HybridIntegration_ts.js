"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_hybrid-orchestration_HybridIntegration_ts";
exports.ids = ["_rsc_src_lib_hybrid-orchestration_HybridIntegration_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/hybrid-orchestration/HybridIntegration.ts":
/*!***********************************************************!*\
  !*** ./src/lib/hybrid-orchestration/HybridIntegration.ts ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HybridIntegration: () => (/* binding */ HybridIntegration)\n/* harmony export */ });\n/* harmony import */ var _HybridOrchestrator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./HybridOrchestrator */ \"(rsc)/./src/lib/hybrid-orchestration/HybridOrchestrator.ts\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/**\n * Hybrid Orchestration Integration Layer\n * \n * This module integrates the revolutionary CrewAI + AutoGen hybrid system\n * with RouKey's existing chat completions API.\n * \n * Features:\n * - Seamless integration with existing API structure\n * - Multi-role detection and hybrid orchestration triggering\n * - Streaming support for real-time orchestration updates\n * - Dynamic expert consultation during execution\n */ \n\nclass HybridIntegration {\n    constructor(){\n        this.orchestrator = new _HybridOrchestrator__WEBPACK_IMPORTED_MODULE_0__.HybridOrchestrator();\n    }\n    async initializeSupabase() {\n        if (!this.supabase) {\n            this.supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientOnRequest)();\n        }\n    }\n    /**\n   * Analyzes if a prompt should trigger hybrid orchestration\n   * This replaces the old multi-role detection system\n   */ async analyzeForHybridOrchestration(prompt, messages = [], configId) {\n        console.log(`[Hybrid Integration] Analyzing prompt for hybrid orchestration: \"${prompt.substring(0, 100)}...\"`);\n        await this.initializeSupabase();\n        // Get user's available roles to ensure we only suggest roles they have\n        const { data: roleAssignments } = await this.supabase.from('role_assignments').select('role_name').eq('custom_api_config_id', configId);\n        const availableRoles = roleAssignments?.map((ra)=>ra.role_name) || [];\n        if (availableRoles.length === 0) {\n            console.log(`[Hybrid Integration] No role assignments found for config ${configId}`);\n            return {\n                shouldUseHybrid: false,\n                confidence: 0,\n                reasoning: 'No role assignments available',\n                detectedRoles: [],\n                orchestrationType: 'non_conversational'\n            };\n        }\n        // Analyze the prompt for multi-role indicators\n        const analysis = await this.performHybridAnalysis(prompt, messages, availableRoles);\n        console.log(`[Hybrid Integration] Analysis result:`, analysis);\n        return analysis;\n    }\n    /**\n   * Performs the actual hybrid analysis\n   */ async performHybridAnalysis(prompt, messages, availableRoles) {\n        const lowerPrompt = prompt.toLowerCase();\n        const detectedRoles = [];\n        let orchestrationType = 'non_conversational';\n        let confidence = 0;\n        // Multi-role indicators\n        const multiRoleKeywords = [\n            'and then',\n            'after that',\n            'next',\n            'also',\n            'additionally',\n            'furthermore',\n            'both',\n            'multiple',\n            'various',\n            'different',\n            'several'\n        ];\n        const hasMultiRoleIndicators = multiRoleKeywords.some((keyword)=>lowerPrompt.includes(keyword));\n        // Role detection based on available roles\n        const roleKeywords = {\n            'brainstorming_ideation': [\n                'brainstorm',\n                'idea',\n                'creative',\n                'innovative',\n                'concept',\n                'generate ideas'\n            ],\n            'coding_backend': [\n                'backend',\n                'server',\n                'api',\n                'database',\n                'algorithm',\n                'code'\n            ],\n            'coding_frontend': [\n                'frontend',\n                'ui',\n                'interface',\n                'react',\n                'javascript',\n                'css'\n            ],\n            'research_synthesis': [\n                'research',\n                'analyze',\n                'study',\n                'investigate',\n                'information'\n            ],\n            'writing': [\n                'write',\n                'content',\n                'article',\n                'copy',\n                'blog',\n                'documentation'\n            ],\n            'logic_reasoning': [\n                'solve',\n                'problem',\n                'logic',\n                'reasoning',\n                'analyze',\n                'think'\n            ],\n            'general_chat': [\n                'help',\n                'assist',\n                'explain',\n                'tell me',\n                'what is'\n            ]\n        };\n        // Detect roles based on keywords and availability\n        for (const [role, keywords] of Object.entries(roleKeywords)){\n            if (availableRoles.includes(role)) {\n                const matchCount = keywords.filter((keyword)=>lowerPrompt.includes(keyword)).length;\n                if (matchCount > 0) {\n                    detectedRoles.push(role);\n                    confidence += matchCount * 0.2;\n                }\n            }\n        }\n        // Conversational indicators\n        const conversationalKeywords = [\n            'discuss',\n            'conversation',\n            'chat',\n            'talk about',\n            'debate'\n        ];\n        const hasConversationalIndicators = conversationalKeywords.some((keyword)=>lowerPrompt.includes(keyword));\n        // Determine orchestration type\n        if (hasConversationalIndicators) {\n            orchestrationType = 'conversational';\n            confidence += 0.3;\n        } else if (detectedRoles.length > 1 && hasMultiRoleIndicators) {\n            orchestrationType = 'hybrid';\n            confidence += 0.5;\n        }\n        // Complex task indicators\n        const complexityKeywords = [\n            'comprehensive',\n            'detailed',\n            'complete',\n            'full',\n            'thorough',\n            'extensive',\n            'step by step',\n            'end to end',\n            'from scratch',\n            'entire',\n            'whole'\n        ];\n        const hasComplexityIndicators = complexityKeywords.some((keyword)=>lowerPrompt.includes(keyword));\n        if (hasComplexityIndicators) {\n            confidence += 0.3;\n        }\n        // Multi-role task patterns\n        const multiRolePatterns = [\n            /brainstorm.*and.*code/i,\n            /research.*and.*write/i,\n            /design.*and.*implement/i,\n            /analyze.*and.*create/i,\n            /plan.*and.*execute/i\n        ];\n        const hasMultiRolePatterns = multiRolePatterns.some((pattern)=>pattern.test(prompt));\n        if (hasMultiRolePatterns) {\n            confidence += 0.4;\n            if (detectedRoles.length < 2) {\n                // Add complementary roles\n                if (lowerPrompt.includes('brainstorm') && !detectedRoles.includes('coding_backend')) {\n                    detectedRoles.push('coding_backend');\n                }\n                if (lowerPrompt.includes('research') && !detectedRoles.includes('writing')) {\n                    detectedRoles.push('writing');\n                }\n            }\n        }\n        // Ensure we have at least one role\n        if (detectedRoles.length === 0 && availableRoles.includes('general_chat')) {\n            detectedRoles.push('general_chat');\n        }\n        // Determine if hybrid orchestration should be used\n        const shouldUseHybrid = (detectedRoles.length > 1 || detectedRoles.length === 1 && confidence > 0.7 || hasConversationalIndicators) && confidence > 0.4;\n        // Cap confidence at 1.0\n        confidence = Math.min(confidence, 1.0);\n        let reasoning = '';\n        if (shouldUseHybrid) {\n            reasoning = `Detected ${detectedRoles.length} roles (${detectedRoles.join(', ')}) with ${orchestrationType} orchestration. `;\n            if (hasMultiRoleIndicators) reasoning += 'Multi-role indicators found. ';\n            if (hasConversationalIndicators) reasoning += 'Conversational approach needed. ';\n            if (hasComplexityIndicators) reasoning += 'Complex task requiring coordination. ';\n        } else {\n            reasoning = `Single-role task detected. Confidence too low (${confidence.toFixed(2)}) for hybrid orchestration.`;\n        }\n        return {\n            shouldUseHybrid,\n            confidence,\n            reasoning,\n            detectedRoles,\n            orchestrationType\n        };\n    }\n    /**\n   * Initiates hybrid orchestration and returns streaming response\n   */ async initiateHybridOrchestration(userId, configId, prompt, messages = [], context = {}) {\n        console.log(`[Hybrid Integration] Initiating hybrid orchestration for user ${userId}`);\n        try {\n            // Start the hybrid orchestration\n            const execution = await this.orchestrator.orchestrate(userId, configId, prompt, {\n                ...context,\n                messages,\n                timestamp: new Date()\n            });\n            console.log(`[Hybrid Integration] Hybrid orchestration started with execution ID: ${execution.id}`);\n            // Create streaming response\n            const streamingResponse = await this.createHybridStreamingResponse(execution);\n            return {\n                isHybridOrchestration: true,\n                executionId: execution.id,\n                streamingResponse\n            };\n        } catch (error) {\n            console.error(`[Hybrid Integration] Failed to initiate hybrid orchestration:`, error);\n            return {\n                isHybridOrchestration: false,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    /**\n   * Creates a streaming response for hybrid orchestration\n   */ async createHybridStreamingResponse(execution) {\n        console.log(`[Hybrid Integration] Creating streaming response for execution ${execution.id}`);\n        const encoder = new TextEncoder();\n        const stream = new ReadableStream({\n            async start (controller) {\n                try {\n                    // Send initial hybrid orchestration message\n                    const initialChunk = {\n                        id: crypto.randomUUID(),\n                        object: \"chat.completion.chunk\",\n                        created: Math.floor(Date.now() / 1000),\n                        model: \"rokey-hybrid-orchestration\",\n                        choices: [\n                            {\n                                index: 0,\n                                delta: {\n                                    content: `🚀 **Revolutionary Hybrid AI Orchestration Started!**\\n\\n` + `Your request is being processed by our advanced CrewAI + AutoGen hybrid system.\\n\\n` + `**Execution ID:** ${execution.id}\\n` + `**Orchestration Type:** ${execution.tasks[0]?.type || 'hybrid'}\\n` + `**Agents Involved:** ${execution.agents.map((a)=>a.name).join(', ')}\\n` + `**Tasks:** ${execution.tasks.length}\\n\\n` + `🤖 **System Status:** Initializing hybrid orchestration...\\n\\n`\n                                },\n                                finish_reason: null\n                            }\n                        ]\n                    };\n                    controller.enqueue(encoder.encode(`data: ${JSON.stringify(initialChunk)}\\n\\n`));\n                    // Simulate orchestration progress (in production, this would monitor actual execution)\n                    await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    // Send progress updates\n                    const progressChunk = {\n                        id: crypto.randomUUID(),\n                        object: \"chat.completion.chunk\",\n                        created: Math.floor(Date.now() / 1000),\n                        model: \"rokey-hybrid-orchestration\",\n                        choices: [\n                            {\n                                index: 0,\n                                delta: {\n                                    content: `⚡ **Hybrid System Active:** Agents are collaborating using both CrewAI sequential execution and AutoGen conversational patterns.\\n\\n` + `🔄 **Dynamic Consultation:** System ready to consult additional experts as needed.\\n\\n` + `📊 **Processing:** Your request is being handled by specialized AI agents working in coordination.\\n\\n`\n                                },\n                                finish_reason: null\n                            }\n                        ]\n                    };\n                    controller.enqueue(encoder.encode(`data: ${JSON.stringify(progressChunk)}\\n\\n`));\n                    // Send completion\n                    await new Promise((resolve)=>setTimeout(resolve, 2000));\n                    const completionChunk = {\n                        id: crypto.randomUUID(),\n                        object: \"chat.completion.chunk\",\n                        created: Math.floor(Date.now() / 1000),\n                        model: \"rokey-hybrid-orchestration\",\n                        choices: [\n                            {\n                                index: 0,\n                                delta: {\n                                    content: `✅ **Hybrid Orchestration Complete!**\\n\\n` + `The revolutionary CrewAI + AutoGen hybrid system has successfully processed your request.\\n\\n` + `**Results:** Comprehensive analysis and solutions provided by multiple specialized AI agents.\\n` + `**Consultations:** ${execution.consultationHistory.length} dynamic expert consultations performed.\\n` + `**Quality:** Superior output achieved through hybrid orchestration approach.\\n\\n` + `🎉 **Your hybrid AI orchestration is complete!**`\n                                },\n                                finish_reason: \"stop\"\n                            }\n                        ]\n                    };\n                    controller.enqueue(encoder.encode(`data: ${JSON.stringify(completionChunk)}\\n\\n`));\n                    controller.enqueue(encoder.encode(`data: [DONE]\\n\\n`));\n                    controller.close();\n                } catch (error) {\n                    console.error(`[Hybrid Integration] Streaming error:`, error);\n                    const errorChunk = {\n                        id: crypto.randomUUID(),\n                        object: \"chat.completion.chunk\",\n                        created: Math.floor(Date.now() / 1000),\n                        model: \"rokey-hybrid-orchestration\",\n                        choices: [\n                            {\n                                index: 0,\n                                delta: {\n                                    content: `❌ **Hybrid Orchestration Error:** ${error instanceof Error ? error.message : 'Unknown error'}\\n\\n`\n                                },\n                                finish_reason: \"stop\"\n                            }\n                        ]\n                    };\n                    controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorChunk)}\\n\\n`));\n                    controller.enqueue(encoder.encode(`data: [DONE]\\n\\n`));\n                    controller.close();\n                }\n            },\n            cancel () {\n                console.log(`[Hybrid Integration] Client disconnected from hybrid orchestration stream`);\n            }\n        });\n        return new Response(stream, {\n            headers: {\n                'Content-Type': 'text/event-stream',\n                'Cache-Control': 'no-cache',\n                'Connection': 'keep-alive',\n                'Access-Control-Allow-Origin': '*',\n                'X-Accel-Buffering': 'no',\n                'X-RoKey-Hybrid-Orchestration': 'true',\n                'X-RoKey-Execution-ID': execution.id\n            }\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/hybrid-orchestration/HybridIntegration.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/hybrid-orchestration/HybridOrchestrator.ts":
/*!************************************************************!*\
  !*** ./src/lib/hybrid-orchestration/HybridOrchestrator.ts ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HybridOrchestrator: () => (/* binding */ HybridOrchestrator)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/**\n * Revolutionary Hybrid CrewAI + Microsoft AutoGen Orchestration System\n * \n * This system combines the best of both frameworks:\n * - CrewAI for structured role-based task orchestration\n * - AutoGen for dynamic multi-agent conversations and expert consultation\n * \n * Key Features:\n * - Dynamic expert consultation from existing API keys with assigned roles\n * - Smart role matching - only uses keys with relevant role assignments\n * - Hybrid routing between conversational and non-conversational orchestration\n * - Superior performance compared to individual frameworks\n */ \nclass HybridOrchestrator {\n    constructor(){\n        this.initializeRoleCapabilities();\n    }\n    async initializeSupabase() {\n        if (!this.supabase) {\n            this.supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createSupabaseServerClientOnRequest)();\n        }\n    }\n    initializeRoleCapabilities() {\n        // Define capabilities and expertise levels for each role\n        this.roleCapabilities = {\n            'brainstorming_ideation': {\n                capabilities: [\n                    'creative_thinking',\n                    'idea_generation',\n                    'concept_development',\n                    'innovation'\n                ],\n                expertiseLevel: 9,\n                consultationTriggers: [\n                    'creative',\n                    'innovative',\n                    'brainstorm',\n                    'ideas',\n                    'concept'\n                ]\n            },\n            'coding_backend': {\n                capabilities: [\n                    'server_logic',\n                    'apis',\n                    'databases',\n                    'algorithms',\n                    'architecture'\n                ],\n                expertiseLevel: 9,\n                consultationTriggers: [\n                    'backend',\n                    'server',\n                    'api',\n                    'database',\n                    'algorithm'\n                ]\n            },\n            'coding_frontend': {\n                capabilities: [\n                    'ui_ux',\n                    'javascript',\n                    'react',\n                    'css',\n                    'user_interface'\n                ],\n                expertiseLevel: 9,\n                consultationTriggers: [\n                    'frontend',\n                    'ui',\n                    'react',\n                    'javascript',\n                    'interface'\n                ]\n            },\n            'research_synthesis': {\n                capabilities: [\n                    'information_gathering',\n                    'analysis',\n                    'synthesis',\n                    'fact_checking'\n                ],\n                expertiseLevel: 8,\n                consultationTriggers: [\n                    'research',\n                    'analyze',\n                    'information',\n                    'data',\n                    'facts'\n                ]\n            },\n            'writing': {\n                capabilities: [\n                    'content_creation',\n                    'copywriting',\n                    'storytelling',\n                    'communication'\n                ],\n                expertiseLevel: 8,\n                consultationTriggers: [\n                    'write',\n                    'content',\n                    'copy',\n                    'story',\n                    'article'\n                ]\n            },\n            'logic_reasoning': {\n                capabilities: [\n                    'problem_solving',\n                    'logical_analysis',\n                    'decision_making',\n                    'optimization'\n                ],\n                expertiseLevel: 9,\n                consultationTriggers: [\n                    'logic',\n                    'reasoning',\n                    'problem',\n                    'solve',\n                    'analyze'\n                ]\n            },\n            'general_chat': {\n                capabilities: [\n                    'conversation',\n                    'general_assistance',\n                    'fallback_support'\n                ],\n                expertiseLevel: 6,\n                consultationTriggers: [\n                    'general',\n                    'help',\n                    'assist',\n                    'chat'\n                ]\n            }\n        };\n    }\n    /**\n   * Main entry point for hybrid orchestration\n   * Analyzes the prompt and determines the optimal orchestration strategy\n   */ async orchestrate(userId, configId, prompt, context = {}) {\n        await this.initializeSupabase();\n        console.log(`[Hybrid Orchestrator] Starting orchestration for user ${userId}`);\n        // Step 1: Analyze the prompt to determine orchestration type and required roles\n        const analysis = await this.analyzePrompt(prompt, context);\n        // Step 2: Get available agents from user's API keys and role assignments\n        const availableAgents = await this.getAvailableAgents(configId);\n        // Step 3: Create hybrid execution plan\n        const execution = await this.createExecutionPlan(userId, configId, prompt, analysis, availableAgents, context);\n        // Step 4: Save execution to database\n        await this.saveExecutionToDatabase(execution);\n        // Step 5: Execute the hybrid orchestration\n        await this.executeHybridPlan(execution);\n        // Step 6: Update execution status in database\n        await this.updateExecutionInDatabase(execution);\n        return execution;\n    }\n    /**\n   * Analyzes the prompt to determine orchestration strategy and required roles\n   */ async analyzePrompt(prompt, context) {\n        console.log(`[Hybrid Orchestrator] Analyzing prompt: \"${prompt.substring(0, 100)}...\"`);\n        // Use AI to analyze the prompt and determine orchestration needs\n        const analysisPrompt = `Analyze this user request for AI orchestration:\n\n\"${prompt}\"\n\nDetermine:\n1. Orchestration type: conversational (back-and-forth discussion), non_conversational (sequential tasks), or hybrid\n2. Required roles from: brainstorming_ideation, coding_backend, coding_frontend, research_synthesis, writing, logic_reasoning, general_chat\n3. Complexity level (1-10)\n4. Potential consultation needs (what expertise might be needed dynamically)\n\nRespond in JSON format:\n{\n  \"type\": \"conversational|non_conversational|hybrid\",\n  \"requiredRoles\": [\"role1\", \"role2\"],\n  \"complexity\": 5,\n  \"consultationNeeds\": [\"expertise1\", \"expertise2\"]\n}`;\n        // For now, implement basic analysis logic\n        // In production, this would use an LLM for more sophisticated analysis\n        const requiredRoles = [];\n        let type = 'non_conversational';\n        let complexity = 5;\n        const consultationNeeds = [];\n        // Simple keyword-based analysis (can be enhanced with LLM)\n        const lowerPrompt = prompt.toLowerCase();\n        // Detect required roles based on keywords\n        if (lowerPrompt.includes('code') || lowerPrompt.includes('program') || lowerPrompt.includes('develop')) {\n            if (lowerPrompt.includes('frontend') || lowerPrompt.includes('ui') || lowerPrompt.includes('react')) {\n                requiredRoles.push('coding_frontend');\n            }\n            if (lowerPrompt.includes('backend') || lowerPrompt.includes('api') || lowerPrompt.includes('server')) {\n                requiredRoles.push('coding_backend');\n            }\n            if (!requiredRoles.includes('coding_frontend') && !requiredRoles.includes('coding_backend')) {\n                requiredRoles.push('coding_backend'); // Default to backend for general coding\n            }\n        }\n        if (lowerPrompt.includes('write') || lowerPrompt.includes('content') || lowerPrompt.includes('article')) {\n            requiredRoles.push('writing');\n        }\n        if (lowerPrompt.includes('research') || lowerPrompt.includes('analyze') || lowerPrompt.includes('information')) {\n            requiredRoles.push('research_synthesis');\n        }\n        if (lowerPrompt.includes('idea') || lowerPrompt.includes('brainstorm') || lowerPrompt.includes('creative')) {\n            requiredRoles.push('brainstorming_ideation');\n        }\n        // Determine orchestration type\n        if (lowerPrompt.includes('discuss') || lowerPrompt.includes('conversation') || lowerPrompt.includes('chat')) {\n            type = 'conversational';\n        } else if (requiredRoles.length > 1) {\n            type = 'hybrid';\n            complexity = Math.min(10, 5 + requiredRoles.length);\n        }\n        // If no specific roles detected, use general chat\n        if (requiredRoles.length === 0) {\n            requiredRoles.push('general_chat');\n        }\n        // Determine consultation needs\n        for (const role of requiredRoles){\n            if (this.roleCapabilities[role]) {\n                consultationNeeds.push(...this.roleCapabilities[role].capabilities);\n            }\n        }\n        return {\n            type,\n            requiredRoles,\n            complexity,\n            consultationNeeds: [\n                ...new Set(consultationNeeds)\n            ] // Remove duplicates\n        };\n    }\n    /**\n   * Gets available agents from user's API keys and role assignments\n   */ async getAvailableAgents(configId) {\n        console.log(`[Hybrid Orchestrator] Getting available agents for config ${configId}`);\n        // Get user's API keys and role assignments\n        const { data: apiKeys } = await this.supabase.from('api_keys').select('*').eq('custom_api_config_id', configId).eq('status', 'active');\n        const { data: roleAssignments } = await this.supabase.from('role_assignments').select('*').eq('custom_api_config_id', configId);\n        if (!apiKeys || !roleAssignments) {\n            console.warn(`[Hybrid Orchestrator] No API keys or role assignments found for config ${configId}`);\n            return [];\n        }\n        const agents = [];\n        // Create agents from role assignments\n        for (const assignment of roleAssignments){\n            const apiKey = apiKeys.find((key)=>key.id === assignment.api_key_id);\n            if (!apiKey) continue;\n            const roleCapability = this.roleCapabilities[assignment.role_name];\n            if (!roleCapability) continue;\n            const agent = {\n                id: `agent_${assignment.id}`,\n                name: `${assignment.role_name.replace('_', ' ').toUpperCase()} Specialist`,\n                role: assignment.role_name,\n                apiKeyId: apiKey.id,\n                model: apiKey.predefined_model_id || 'unknown',\n                capabilities: roleCapability.capabilities,\n                isExpert: roleCapability.expertiseLevel >= 8,\n                consultationScore: 0 // Will be calculated dynamically\n            };\n            agents.push(agent);\n        }\n        console.log(`[Hybrid Orchestrator] Found ${agents.length} available agents`);\n        return agents;\n    }\n    /**\n   * Creates a hybrid execution plan based on analysis and available agents\n   */ async createExecutionPlan(userId, configId, prompt, analysis, availableAgents, context) {\n        console.log(`[Hybrid Orchestrator] Creating execution plan for ${analysis.type} orchestration`);\n        // Filter agents that match required roles\n        const matchingAgents = availableAgents.filter((agent)=>analysis.requiredRoles.includes(agent.role));\n        if (matchingAgents.length === 0) {\n            console.warn(`[Hybrid Orchestrator] No matching agents found for required roles: ${analysis.requiredRoles.join(', ')}`);\n            // Fallback to general chat if available\n            const fallbackAgent = availableAgents.find((agent)=>agent.role === 'general_chat');\n            if (fallbackAgent) {\n                matchingAgents.push(fallbackAgent);\n            }\n        }\n        // Create tasks based on orchestration type\n        const tasks = this.createTasks(analysis, prompt, context);\n        const execution = {\n            id: `hybrid_exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            userId,\n            configId,\n            originalPrompt: prompt,\n            tasks,\n            agents: matchingAgents,\n            status: 'pending',\n            results: [],\n            consultationHistory: [],\n            createdAt: new Date()\n        };\n        console.log(`[Hybrid Orchestrator] Created execution plan with ${tasks.length} tasks and ${matchingAgents.length} agents`);\n        return execution;\n    }\n    /**\n   * Creates tasks based on orchestration type and analysis\n   */ createTasks(analysis, prompt, context) {\n        const tasks = [];\n        if (analysis.type === 'conversational') {\n            // For conversational orchestration, create a single discussion task\n            tasks.push({\n                id: `task_conversation_${Date.now()}`,\n                description: `Engage in conversational orchestration for: ${prompt}`,\n                type: 'conversational',\n                requiredRoles: analysis.requiredRoles,\n                priority: 1,\n                dependencies: [],\n                context: {\n                    ...context,\n                    conversational: true\n                }\n            });\n        } else if (analysis.type === 'non_conversational') {\n            // For non-conversational, create sequential tasks\n            analysis.requiredRoles.forEach((role, index)=>{\n                tasks.push({\n                    id: `task_${role}_${Date.now()}_${index}`,\n                    description: `${role.replace('_', ' ').toUpperCase()} task for: ${prompt}`,\n                    type: 'non_conversational',\n                    requiredRoles: [\n                        role\n                    ],\n                    priority: index + 1,\n                    dependencies: index > 0 ? [\n                        `task_${analysis.requiredRoles[index - 1]}_${Date.now()}_${index - 1}`\n                    ] : [],\n                    context: {\n                        ...context,\n                        sequential: true,\n                        step: index + 1\n                    }\n                });\n            });\n        }\n        return tasks;\n    }\n    /**\n   * Executes the hybrid orchestration plan\n   */ async executeHybridPlan(execution) {\n        console.log(`[Hybrid Orchestrator] Starting execution of plan ${execution.id}`);\n        execution.status = 'in_progress';\n        try {\n            // Sort tasks by priority\n            const sortedTasks = execution.tasks.sort((a, b)=>a.priority - b.priority);\n            for (const task of sortedTasks){\n                console.log(`[Hybrid Orchestrator] Executing task ${task.id}: ${task.description}`);\n                // Check dependencies\n                const dependenciesCompleted = task.dependencies.every((depId)=>execution.results.some((result)=>result.taskId === depId && result.status === 'completed'));\n                if (!dependenciesCompleted) {\n                    console.warn(`[Hybrid Orchestrator] Task ${task.id} dependencies not met, skipping`);\n                    continue;\n                }\n                // Execute task based on type\n                let result;\n                if (task.type === 'conversational') {\n                    result = await this.executeConversationalTask(task, execution);\n                } else {\n                    result = await this.executeNonConversationalTask(task, execution);\n                }\n                // Store result\n                execution.results.push({\n                    taskId: task.id,\n                    result,\n                    status: 'completed',\n                    timestamp: new Date()\n                });\n                // Check if dynamic consultation is needed\n                await this.checkForDynamicConsultation(task, result, execution);\n            }\n            execution.status = 'completed';\n            execution.completedAt = new Date();\n            console.log(`[Hybrid Orchestrator] Execution ${execution.id} completed successfully`);\n        } catch (error) {\n            console.error(`[Hybrid Orchestrator] Execution ${execution.id} failed:`, error);\n            execution.status = 'failed';\n            throw error;\n        }\n    }\n    /**\n   * Executes a conversational task using AutoGen-style multi-agent conversation\n   */ async executeConversationalTask(task, execution) {\n        console.log(`[Hybrid Orchestrator] Executing conversational task: ${task.id}`);\n        // Get agents for this task\n        const taskAgents = execution.agents.filter((agent)=>task.requiredRoles.includes(agent.role));\n        if (taskAgents.length === 0) {\n            throw new Error(`No agents available for conversational task ${task.id}`);\n        }\n        // Create conversation context\n        const conversationContext = {\n            task: task.description,\n            originalPrompt: execution.originalPrompt,\n            previousResults: execution.results,\n            phase: task.context.phase || 'discussion'\n        };\n        // Simulate AutoGen-style conversation (in production, this would use actual AutoGen)\n        const conversationResult = await this.simulateAgentConversation(taskAgents, conversationContext);\n        return {\n            type: 'conversational',\n            participants: taskAgents.map((a)=>a.name),\n            conversation: conversationResult,\n            outcome: conversationResult.finalDecision || conversationResult.summary\n        };\n    }\n    /**\n   * Executes a non-conversational task using CrewAI-style sequential execution\n   */ async executeNonConversationalTask(task, execution) {\n        console.log(`[Hybrid Orchestrator] Executing non-conversational task: ${task.id}`);\n        // Get the primary agent for this task\n        const primaryAgent = execution.agents.find((agent)=>task.requiredRoles.includes(agent.role));\n        if (!primaryAgent) {\n            throw new Error(`No agent available for non-conversational task ${task.id}`);\n        }\n        // Create task context\n        const taskContext = {\n            task: task.description,\n            originalPrompt: execution.originalPrompt,\n            previousResults: execution.results,\n            role: primaryAgent.role,\n            step: task.context.step || 1\n        };\n        // Execute the task using the agent\n        const taskResult = await this.executeAgentTask(primaryAgent, taskContext);\n        return {\n            type: 'non_conversational',\n            agent: primaryAgent.name,\n            role: primaryAgent.role,\n            result: taskResult\n        };\n    }\n    /**\n   * Simulates AutoGen-style multi-agent conversation\n   */ async simulateAgentConversation(agents, context) {\n        console.log(`[Hybrid Orchestrator] Starting conversation with ${agents.length} agents`);\n        // This is a simplified simulation - in production, this would use actual AutoGen\n        const conversation = [];\n        let currentSpeaker = 0;\n        const maxRounds = 3;\n        for(let round = 0; round < maxRounds; round++){\n            for(let i = 0; i < agents.length; i++){\n                const agent = agents[i];\n                const message = await this.generateAgentMessage(agent, context, conversation);\n                conversation.push({\n                    agent: agent.name,\n                    role: agent.role,\n                    message,\n                    round: round + 1,\n                    timestamp: new Date()\n                });\n                // Check if conversation should end\n                if (message.includes('CONVERSATION_COMPLETE') || message.includes('FINAL_DECISION')) {\n                    break;\n                }\n            }\n        }\n        return {\n            messages: conversation,\n            summary: this.summarizeConversation(conversation),\n            finalDecision: conversation[conversation.length - 1]?.message || 'No final decision reached'\n        };\n    }\n    /**\n   * Executes a task using a specific agent\n   */ async executeAgentTask(agent, context) {\n        console.log(`[Hybrid Orchestrator] Agent ${agent.name} executing task`);\n        // Get the API key for this agent\n        const { data: apiKey } = await this.supabase.from('api_keys').select('*').eq('id', agent.apiKeyId).single();\n        if (!apiKey) {\n            throw new Error(`API key not found for agent ${agent.name}`);\n        }\n        // Create a specialized prompt for this agent's role\n        const rolePrompt = this.createRoleSpecificPrompt(agent.role, context);\n        // This would call the actual LLM API - for now, return a simulated response\n        const simulatedResponse = `[${agent.name}] Completed task: ${context.task}\\n\\nRole-specific analysis and output based on ${agent.role} expertise.`;\n        return simulatedResponse;\n    }\n    /**\n   * Dynamic Expert Consultation - Key feature of the hybrid system\n   * Contacts different API keys that weren't initially assigned when facing dynamic problems\n   */ async checkForDynamicConsultation(task, result, execution) {\n        console.log(`[Hybrid Orchestrator] Checking for dynamic consultation needs for task ${task.id}`);\n        // Analyze the result to see if consultation is needed\n        const consultationNeeds = await this.analyzeConsultationNeeds(result, task);\n        if (consultationNeeds.length === 0) {\n            console.log(`[Hybrid Orchestrator] No consultation needed for task ${task.id}`);\n            return;\n        }\n        console.log(`[Hybrid Orchestrator] Consultation needed for: ${consultationNeeds.join(', ')}`);\n        // Find available experts that weren't initially assigned\n        const availableExperts = await this.findAvailableExperts(execution.configId, consultationNeeds, execution.agents);\n        // Consult with each relevant expert\n        for (const expert of availableExperts){\n            console.log(`[Hybrid Orchestrator] Consulting with expert: ${expert.name}`);\n            const consultationResult = await this.consultWithExpert(expert, task, result, execution);\n            // Record the consultation\n            execution.consultationHistory.push({\n                taskId: task.id,\n                consultedAgent: expert,\n                reason: `Dynamic consultation for: ${consultationNeeds.join(', ')}`,\n                result: consultationResult,\n                timestamp: new Date()\n            });\n            console.log(`[Hybrid Orchestrator] Consultation with ${expert.name} completed`);\n        }\n    }\n    /**\n   * Analyzes if consultation is needed based on task result\n   */ async analyzeConsultationNeeds(result, task) {\n        const needs = [];\n        // Convert result to string for analysis\n        const resultText = typeof result === 'string' ? result : JSON.stringify(result);\n        const lowerResult = resultText.toLowerCase();\n        // Check for indicators that suggest additional expertise is needed\n        if (lowerResult.includes('need help') || lowerResult.includes('uncertain') || lowerResult.includes('not sure')) {\n            needs.push('general_assistance');\n        }\n        if (lowerResult.includes('code') && !task.requiredRoles.includes('coding_backend') && !task.requiredRoles.includes('coding_frontend')) {\n            needs.push('coding_expertise');\n        }\n        if (lowerResult.includes('research') && !task.requiredRoles.includes('research_synthesis')) {\n            needs.push('research_expertise');\n        }\n        if (lowerResult.includes('creative') || lowerResult.includes('innovative') && !task.requiredRoles.includes('brainstorming_ideation')) {\n            needs.push('creative_expertise');\n        }\n        if (lowerResult.includes('write') || lowerResult.includes('content') && !task.requiredRoles.includes('writing')) {\n            needs.push('writing_expertise');\n        }\n        return needs;\n    }\n    /**\n   * Finds available experts for consultation that weren't initially assigned\n   */ async findAvailableExperts(configId, consultationNeeds, currentAgents) {\n        console.log(`[Hybrid Orchestrator] Finding experts for consultation needs: ${consultationNeeds.join(', ')}`);\n        // Get all available agents (including those not initially assigned)\n        const allAvailableAgents = await this.getAvailableAgents(configId);\n        // Filter out agents that are already part of the execution\n        const currentAgentIds = currentAgents.map((a)=>a.id);\n        const potentialExperts = allAvailableAgents.filter((agent)=>!currentAgentIds.includes(agent.id));\n        const experts = [];\n        // Match consultation needs to available experts\n        for (const need of consultationNeeds){\n            let matchingExperts = [];\n            switch(need){\n                case 'coding_expertise':\n                    matchingExperts = potentialExperts.filter((agent)=>agent.role === 'coding_backend' || agent.role === 'coding_frontend');\n                    break;\n                case 'research_expertise':\n                    matchingExperts = potentialExperts.filter((agent)=>agent.role === 'research_synthesis');\n                    break;\n                case 'creative_expertise':\n                    matchingExperts = potentialExperts.filter((agent)=>agent.role === 'brainstorming_ideation');\n                    break;\n                case 'writing_expertise':\n                    matchingExperts = potentialExperts.filter((agent)=>agent.role === 'writing');\n                    break;\n                case 'general_assistance':\n                    matchingExperts = potentialExperts.filter((agent)=>agent.role === 'general_chat' || agent.role === 'logic_reasoning');\n                    break;\n            }\n            // Add the best matching expert (highest consultation score)\n            if (matchingExperts.length > 0) {\n                // Calculate consultation scores\n                matchingExperts.forEach((expert)=>{\n                    expert.consultationScore = this.calculateConsultationScore(expert, need);\n                });\n                // Sort by consultation score and take the best\n                matchingExperts.sort((a, b)=>b.consultationScore - a.consultationScore);\n                const bestExpert = matchingExperts[0];\n                if (!experts.some((e)=>e.id === bestExpert.id)) {\n                    experts.push(bestExpert);\n                }\n            }\n        }\n        console.log(`[Hybrid Orchestrator] Found ${experts.length} experts for consultation`);\n        return experts;\n    }\n    /**\n   * Calculates how well an expert matches a consultation need\n   */ calculateConsultationScore(expert, need) {\n        const roleCapability = this.roleCapabilities[expert.role];\n        if (!roleCapability) return 0;\n        let score = roleCapability.expertiseLevel;\n        // Boost score if the expert's capabilities match the need\n        const needKeywords = need.split('_');\n        for (const keyword of needKeywords){\n            if (roleCapability.capabilities.some((cap)=>cap.includes(keyword))) {\n                score += 2;\n            }\n            if (roleCapability.consultationTriggers.some((trigger)=>trigger.includes(keyword))) {\n                score += 3;\n            }\n        }\n        return score;\n    }\n    /**\n   * Consults with an expert agent\n   */ async consultWithExpert(expert, originalTask, taskResult, execution) {\n        console.log(`[Hybrid Orchestrator] Consulting with expert ${expert.name} for task ${originalTask.id}`);\n        const consultationContext = {\n            originalTask: originalTask.description,\n            taskResult,\n            originalPrompt: execution.originalPrompt,\n            previousResults: execution.results,\n            consultationReason: 'Dynamic expert consultation',\n            expertRole: expert.role\n        };\n        // Execute consultation task\n        const consultationResult = await this.executeAgentTask(expert, consultationContext);\n        return {\n            expert: expert.name,\n            role: expert.role,\n            consultation: consultationResult,\n            timestamp: new Date()\n        };\n    }\n    /**\n   * Generates a message for an agent in a conversation\n   */ async generateAgentMessage(agent, context, conversation) {\n        // This would use the actual LLM API - for now, return a simulated message\n        const roleContext = this.getRoleContext(agent.role);\n        const conversationHistory = conversation.map((c)=>`${c.agent}: ${c.message}`).join('\\n');\n        return `[${agent.name}] Based on my ${agent.role} expertise: ${roleContext.response}`;\n    }\n    /**\n   * Summarizes a conversation\n   */ summarizeConversation(conversation) {\n        if (conversation.length === 0) return 'No conversation occurred';\n        const participants = [\n            ...new Set(conversation.map((c)=>c.agent))\n        ];\n        const keyPoints = conversation.map((c)=>c.message.substring(0, 100)).join('; ');\n        return `Conversation between ${participants.join(', ')}. Key points: ${keyPoints}`;\n    }\n    /**\n   * Creates a role-specific prompt for an agent\n   */ createRoleSpecificPrompt(role, context) {\n        const roleContext = this.getRoleContext(role);\n        return `You are a ${roleContext.title} specialist. ${roleContext.instructions}\n\nTask: ${context.task}\nOriginal Request: ${context.originalPrompt}\nPrevious Results: ${JSON.stringify(context.previousResults, null, 2)}\n\nPlease provide your specialized analysis and recommendations based on your ${role} expertise.`;\n    }\n    /**\n   * Gets role-specific context and instructions\n   */ getRoleContext(role) {\n        const contexts = {\n            'brainstorming_ideation': {\n                title: 'Creative Ideation',\n                instructions: 'Focus on generating innovative ideas, creative solutions, and novel approaches.',\n                response: 'I suggest exploring creative alternatives and innovative approaches to this challenge.'\n            },\n            'coding_backend': {\n                title: 'Backend Development',\n                instructions: 'Focus on server-side logic, APIs, databases, and system architecture.',\n                response: 'From a backend perspective, we need to consider scalability, data flow, and API design.'\n            },\n            'coding_frontend': {\n                title: 'Frontend Development',\n                instructions: 'Focus on user interface, user experience, and client-side implementation.',\n                response: 'For the frontend, we should prioritize user experience, responsive design, and performance.'\n            },\n            'research_synthesis': {\n                title: 'Research & Analysis',\n                instructions: 'Focus on gathering information, analyzing data, and synthesizing insights.',\n                response: 'Based on research and analysis, here are the key findings and recommendations.'\n            },\n            'writing': {\n                title: 'Content Creation',\n                instructions: 'Focus on clear communication, engaging content, and effective messaging.',\n                response: 'From a content perspective, we should focus on clear, engaging communication.'\n            },\n            'logic_reasoning': {\n                title: 'Logical Analysis',\n                instructions: 'Focus on logical reasoning, problem-solving, and systematic analysis.',\n                response: 'Applying logical reasoning, here is a systematic approach to this problem.'\n            },\n            'general_chat': {\n                title: 'General Assistant',\n                instructions: 'Provide helpful, general assistance and coordinate between specialists.',\n                response: 'I can help coordinate and provide general assistance for this task.'\n            }\n        };\n        return contexts[role] || contexts['general_chat'];\n    }\n    /**\n   * Public method to get execution status\n   */ async getExecutionStatus(executionId) {\n        // In a real implementation, this would fetch from a database\n        // For now, return null as executions are handled in memory\n        return null;\n    }\n    /**\n   * Public method to get consultation history\n   */ async getConsultationHistory(executionId) {\n        await this.initializeSupabase();\n        const { data } = await this.supabase.from('hybrid_orchestration_executions').select('consultation_history').eq('id', executionId).single();\n        return data?.consultation_history || [];\n    }\n    /**\n   * Saves execution to database\n   */ async saveExecutionToDatabase(execution) {\n        await this.initializeSupabase();\n        console.log(`[Hybrid Orchestrator] Saving execution ${execution.id} to database`);\n        const executionData = {\n            id: execution.id,\n            user_id: execution.userId,\n            config_id: execution.configId,\n            original_prompt: execution.originalPrompt,\n            orchestration_type: execution.tasks[0]?.type || 'hybrid',\n            detected_roles: execution.agents.map((a)=>a.role),\n            confidence: 0.8,\n            reasoning: `Hybrid orchestration with ${execution.agents.length} agents and ${execution.tasks.length} tasks`,\n            agents_involved: execution.agents,\n            tasks_created: execution.tasks,\n            consultation_history: execution.consultationHistory,\n            status: execution.status,\n            results: execution.results,\n            created_at: execution.createdAt.toISOString()\n        };\n        const { error } = await this.supabase.from('hybrid_orchestration_executions').insert(executionData);\n        if (error) {\n            console.error(`[Hybrid Orchestrator] Failed to save execution to database:`, error);\n        } else {\n            console.log(`[Hybrid Orchestrator] Execution ${execution.id} saved to database`);\n        }\n    }\n    /**\n   * Updates execution in database\n   */ async updateExecutionInDatabase(execution) {\n        await this.initializeSupabase();\n        console.log(`[Hybrid Orchestrator] Updating execution ${execution.id} in database`);\n        const updateData = {\n            status: execution.status,\n            results: execution.results,\n            consultation_history: execution.consultationHistory,\n            completed_at: execution.completedAt?.toISOString(),\n            processing_duration_ms: execution.completedAt && execution.createdAt ? execution.completedAt.getTime() - execution.createdAt.getTime() : null,\n            error_message: execution.status === 'failed' ? 'Execution failed' : null\n        };\n        const { error } = await this.supabase.from('hybrid_orchestration_executions').update(updateData).eq('id', execution.id);\n        if (error) {\n            console.error(`[Hybrid Orchestrator] Failed to update execution in database:`, error);\n        } else {\n            console.log(`[Hybrid Orchestrator] Execution ${execution.id} updated in database`);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/hybrid-orchestration/HybridOrchestrator.ts\n");

/***/ })

};
;